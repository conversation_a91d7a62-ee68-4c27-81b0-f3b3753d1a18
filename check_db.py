import sqlite3

try:
    conn = sqlite3.connect('paper.db')
    c = conn.cursor()
    
    # Check tables
    tables = c.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
    print("Tables:", tables)
    
    # If papers table exists, check its structure
    if ('papers',) in tables:
        schema = c.execute("PRAGMA table_info(papers)").fetchall()
        print("papers table schema:", schema)

        # Check row count
        count = c.execute("SELECT COUNT(*) FROM papers").fetchone()[0]
        print("Row count:", count)
    
    conn.close()
except Exception as e:
    print("Error:", e)
